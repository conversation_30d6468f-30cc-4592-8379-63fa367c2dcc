// 使用第三方包的示例 - colors 包让控制台输出更漂亮
const colors = require('colors');

console.log('🌈 欢迎来到彩色的 Node.js 世界！'.rainbow);

// 不同颜色的文字
console.log('这是红色文字'.red);
console.log('这是绿色文字'.green);
console.log('这是蓝色文字'.blue);
console.log('这是黄色文字'.yellow);
console.log('这是紫色文字'.magenta);
console.log('这是青色文字'.cyan);

// 背景色
console.log('这是红色背景'.bgRed);
console.log('这是绿色背景'.bgGreen);

// 文字样式
console.log('这是粗体文字'.bold);
console.log('这是下划线文字'.underline);
console.log('这是斜体文字'.italic);

// 组合样式
console.log('这是粗体红色文字'.bold.red);
console.log('这是下划线蓝色背景文字'.underline.bgBlue);

// 创建一个彩色的菜单
console.log('\n📋 彩色菜单：'.bold.cyan);
console.log('1. 开始游戏'.green);
console.log('2. 查看设置'.yellow);
console.log('3. 退出程序'.red);

// 创建一个状态指示器
function showStatus(status, message) {
    switch(status) {
        case 'success':
            console.log('✅ 成功：'.green + message);
            break;
        case 'warning':
            console.log('⚠️  警告：'.yellow + message);
            break;
        case 'error':
            console.log('❌ 错误：'.red + message);
            break;
        case 'info':
            console.log('ℹ️  信息：'.blue + message);
            break;
        default:
            console.log(message);
    }
}

console.log('\n📊 状态示例：'.bold);
showStatus('success', '文件上传完成');
showStatus('warning', '磁盘空间不足');
showStatus('error', '网络连接失败');
showStatus('info', '正在处理请求...');

// 创建一个进度条效果
console.log('\n⏳ 模拟进度条：'.bold);
const progressChars = ['▱', '▰'];
let progress = 0;

const progressInterval = setInterval(() => {
    const filled = Math.floor(progress / 10);
    const empty = 10 - filled;
    
    const bar = progressChars[1].repeat(filled).green + 
                progressChars[0].repeat(empty).gray;
    
    process.stdout.write(`\r进度: [${bar}] ${progress}%`);
    
    progress += 10;
    
    if (progress > 100) {
        clearInterval(progressInterval);
        console.log('\n✨ 完成！'.bold.green);
        
        // 显示总结
        console.log('\n🎉 恭喜你！'.rainbow.bold);
        console.log('你已经学会了：'.cyan);
        console.log('• 运行 Node.js 程序'.green);
        console.log('• 使用 npm 安装包'.green);
        console.log('• 使用第三方库'.green);
        console.log('• 创建彩色控制台输出'.green);
        
        console.log('\n💡 下一步建议：'.yellow.bold);
        console.log('1. 尝试安装其他有趣的包');
        console.log('2. 学习创建 Web 服务器');
        console.log('3. 探索 Express.js 框架');
    }
}, 500);

// 显示包信息
console.log('\n📦 关于 colors 包：'.bold.magenta);
console.log('• 这是一个让控制台输出更美观的包');
console.log('• 可以添加颜色、背景色和文字样式');
console.log('• 在 GitHub 上有超过 4000 个星标');
console.log('• 每周下载量超过 1000 万次');
