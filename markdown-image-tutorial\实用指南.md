# 🎯 markdown-to-image 实用指南

## 🚀 快速开始

### 1. 基本安装和使用
```bash
# 安装依赖
npm install react react-dom markdown-to-image

# 在项目中使用
import 'markdown-to-image/dist/style.css';
import { Md2Poster, Md2PosterContent, Md2PosterHeader, Md2PosterFooter } from 'markdown-to-image';
```

### 2. 最简单的使用方式
```jsx
function SimplePoster() {
  const markdown = `# 我的标题\n\n这是内容`;
  
  return (
    <Md2Poster>
      <Md2PosterContent>{markdown}</Md2PosterContent>
    </Md2Poster>
  );
}
```

## 🎨 主题和样式

### 可用主题列表
- `SpringGradientWave` - 春季渐变波浪 (默认)
- `SummerSunset` - 夏日日落
- `AutumnLeaves` - 秋叶
- `WinterSnow` - 冬雪
- `OceanBreeze` - 海洋微风
- `ForestGreen` - 森林绿
- `DesertSand` - 沙漠沙
- `NightSky` - 夜空
- `Rose<PERSON><PERSON>` - 玫瑰金

### 使用不同主题
```jsx
<Md2Poster theme="SummerSunset" size="mobile">
  <Md2PosterContent>{markdown}</Md2PosterContent>
</Md2Poster>
```

## 📐 尺寸选项

### 三种尺寸
- `mobile` - 移动端尺寸 (适合手机分享)
- `desktop` - 桌面端尺寸 (适合电脑展示)  
- `square` - 正方形尺寸 (适合社交媒体)

### 尺寸使用示例
```jsx
// 移动端海报
<Md2Poster size="mobile" theme="SpringGradientWave">
  <Md2PosterContent>{markdown}</Md2PosterContent>
</Md2Poster>

// 正方形海报（适合 Instagram）
<Md2Poster size="square" theme="RoseGold">
  <Md2PosterContent>{markdown}</Md2PosterContent>
</Md2Poster>
```

## 🔧 组件详解

### Md2Poster (主容器)
```jsx
<Md2Poster 
  theme="SpringGradientWave"  // 主题
  size="mobile"               // 尺寸
  className="custom-class"    // 自定义CSS类
>
  {/* 子组件 */}
</Md2Poster>
```

### Md2PosterHeader (头部)
```jsx
<Md2PosterHeader className="flex justify-between items-center px-4">
  <span>@用户名</span>
  <span>{new Date().toLocaleDateString()}</span>
</Md2PosterHeader>
```

### Md2PosterContent (内容)
```jsx
<Md2PosterContent>
  {markdownString}
</Md2PosterContent>
```

### Md2PosterFooter (底部)
```jsx
<Md2PosterFooter className="text-center">
  <span>版权信息或链接</span>
</Md2PosterFooter>
```

## 📝 Markdown 语法支持

### 支持的语法
```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文字**
*斜体文字*

> 引用文字

- 无序列表项1
- 无序列表项2

1. 有序列表项1
2. 有序列表项2

`行内代码`

```javascript
// 代码块
console.log("Hello World");
```

[链接文字](https://example.com)

![图片描述](图片URL)

---
分割线
```

## 🎯 实际应用场景

### 1. 技术分享海报
```jsx
const techContent = `# 🔥 React Hooks 技巧

> 让你的组件更强大

## useState 最佳实践

\`\`\`javascript
const [count, setCount] = useState(0);

// 函数式更新
setCount(prev => prev + 1);
\`\`\`

## 关键要点

- ✅ 只在顶层调用 Hooks
- ✅ 使用函数式更新
- ✅ 合理拆分状态

**记住：Hooks 让函数组件更强大！**`;

<Md2Poster theme="ForestGreen" size="desktop">
  <Md2PosterHeader className="flex justify-between px-4">
    <span>@TechShare</span>
    <span>2024-08-04</span>
  </Md2PosterHeader>
  <Md2PosterContent>{techContent}</Md2PosterContent>
  <Md2PosterFooter className="text-center">
    关注我获取更多技术分享
  </Md2PosterFooter>
</Md2Poster>
```

### 2. 每日总结海报
```jsx
const dailyContent = `# 📅 今日收获

> ${new Date().toLocaleDateString()} 

## 完成事项

✅ 学会了 markdown-to-image  
✅ 创建了第一个技术海报  
✅ 掌握了 React Hooks 用法  

## 明日计划

🎯 深入学习 TypeScript  
🎯 完成项目重构  
🎯 写技术博客  

---

*每天进步一点点 🌟*`;

<Md2Poster theme="SummerSunset" size="square">
  <Md2PosterContent>{dailyContent}</Md2PosterContent>
</Md2Poster>
```

### 3. 励志语录海报
```jsx
const quoteContent = `# 💭 今日金句

> "代码是写给人看的，只是顺便能在机器上运行。"
> 
> —— Harold Abelson

## 编程哲学

- **简洁胜过复杂**
- **可读性很重要**
- **测试是必需的**

---

**让代码成为艺术 ✨**`;

<Md2Poster theme="NightSky" size="mobile">
  <Md2PosterContent>{quoteContent}</Md2PosterContent>
</Md2Poster>
```

## 💡 高级技巧

### 1. 动态内容生成
```jsx
function DynamicPoster({ data }) {
  const markdown = `# ${data.title}

> ${data.subtitle}

${data.items.map((item, index) => 
  `${index + 1}. ${item}`
).join('\n')}

---

*${data.footer}*`;

  return (
    <Md2Poster theme={data.theme} size={data.size}>
      <Md2PosterContent>{markdown}</Md2PosterContent>
    </Md2Poster>
  );
}
```

### 2. 自定义样式
```jsx
<Md2Poster 
  theme="SpringGradientWave" 
  className="my-custom-poster"
  style={{ border: '2px solid #333' }}
>
  <Md2PosterContent>{markdown}</Md2PosterContent>
</Md2Poster>
```

### 3. 响应式设计
```jsx
function ResponsivePoster({ markdown }) {
  const [size, setSize] = useState('mobile');
  
  useEffect(() => {
    const updateSize = () => {
      setSize(window.innerWidth > 768 ? 'desktop' : 'mobile');
    };
    
    window.addEventListener('resize', updateSize);
    updateSize();
    
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <Md2Poster size={size} theme="SpringGradientWave">
      <Md2PosterContent>{markdown}</Md2PosterContent>
    </Md2Poster>
  );
}
```

## 🔧 常见问题解决

### 1. Next.js 中使用
```jsx
import dynamic from 'next/dynamic';

const Md2Poster = dynamic(
  () => import('markdown-to-image').then(mod => mod.Md2Poster),
  { ssr: false }
);
```

### 2. 图片导出
```jsx
import html2canvas from 'html2canvas';

const exportImage = async (elementRef) => {
  const canvas = await html2canvas(elementRef.current);
  const link = document.createElement('a');
  link.download = 'poster.png';
  link.href = canvas.toDataURL();
  link.click();
};
```

### 3. 批量生成
```jsx
function BatchPosterGenerator({ dataList }) {
  return (
    <div>
      {dataList.map((data, index) => (
        <Md2Poster key={index} theme={data.theme} size={data.size}>
          <Md2PosterContent>{data.markdown}</Md2PosterContent>
        </Md2Poster>
      ))}
    </div>
  );
}
```

## 🎯 最佳实践

1. **内容长度控制**: 避免内容过长，影响视觉效果
2. **主题选择**: 根据内容类型选择合适的主题
3. **尺寸适配**: 根据分享平台选择合适的尺寸
4. **样式一致性**: 保持品牌风格的一致性
5. **性能优化**: 大量海报时考虑虚拟化渲染

现在你已经掌握了 markdown-to-image 的核心用法，可以开始创建你自己的漂亮海报了！🚀
