<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Markdown to poster - readpo.com</title>
    <meta
      name="description"
      content="The React component is used to render Markdown into a beautiful poster image, with support for copying as an image. Md to Poster/Image/Quote/Card/Instagram/Twitter/Facebook..."
    />
    <meta
      name="keywords"
      content="Markdown, Poster, Image, Quote, Card, Instagram, Twitter, Facebook, readpo, readpo.com"
    />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
