// 1. 首先安装依赖
// npm install markdown-to-image react react-dom

// 2. 在你的 React 组件中使用
import React from 'react';
import 'markdown-to-image/dist/style.css';
import { Md2Poster, Md2PosterContent, Md2PosterHeader, Md2PosterFooter } from 'markdown-to-image';

function MyFirstPoster() {
  // 你的 Markdown 内容
  const markdown = `# 🚀 我的第一个海报

> 使用 markdown-to-image 创建

## 主要特点

- **简单易用** - 只需写 Markdown
- **主题丰富** - 9种精美主题
- **尺寸灵活** - 适配各种平台

### 代码示例

\`\`\`javascript
console.log("Hello, markdown-to-image!");
\`\`\`

### 使用场景

1. 社交媒体分享
2. 技术文档展示
3. 学习笔记整理

---

**开始你的创作之旅！** ✨`;

  return (
    <div style={{ padding: '20px' }}>
      <h1>我的海报生成器</h1>
      
      {/* 这就是完整的海报组件 */}
      <Md2Poster theme="SpringGradientWave" size="mobile">
        <Md2PosterHeader className="flex justify-between items-center px-4">
          <span>@我的用户名</span>
          <span>{new Date().toISOString().slice(0, 10)}</span>
        </Md2PosterHeader>
        <Md2PosterContent>{markdown}</Md2PosterContent>
        <Md2PosterFooter className="flex justify-center items-center">
          <span>由 markdown-to-image 强力驱动</span>
        </Md2PosterFooter>
      </Md2Poster>
    </div>
  );
}

export default MyFirstPoster;
