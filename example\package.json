{"name": "example", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@next/mdx": "^14.2.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@types/mdx": "^2.0.13", "@uiw/react-md-editor": "^4.0.4", "@vercel/analytics": "^1.2.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.378.0", "markdown-to-image": "^0.0.12", "next": "15.1.6", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}