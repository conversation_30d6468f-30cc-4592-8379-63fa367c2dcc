<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 markdown-to-image</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="./node_modules/markdown-to-image/dist/style.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .poster-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .poster-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .poster-item h3 {
            margin-top: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 markdown-to-image 测试</h1>
            <p>立即体验 Markdown 转图片功能</p>
        </div>
        
        <div id="app"></div>
    </div>

    <script type="text/babel">
        const { useState } = React;

        // 模拟 markdown-to-image 组件（因为在 CDN 环境中直接导入有困难）
        function Md2Poster({ theme = 'SpringGradientWave', size = 'mobile', children }) {
            const themeStyles = {
                SpringGradientWave: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                SummerSunset: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                AutumnLeaves: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                WinterSnow: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                OceanBreeze: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
                ForestGreen: 'linear-gradient(135deg, #00b894 0%, #00a085 100%)',
                DesertSand: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)',
                NightSky: 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)',
                RoseGold: 'linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%)'
            };

            const sizeStyles = {
                mobile: { width: '375px', minHeight: '500px' },
                desktop: { width: '800px', minHeight: '600px' },
                square: { width: '500px', minHeight: '500px' }
            };

            return (
                <div style={{
                    ...sizeStyles[size],
                    background: themeStyles[theme],
                    borderRadius: '12px',
                    padding: '20px',
                    color: 'white',
                    fontFamily: 'Arial, sans-serif',
                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    {children}
                </div>
            );
        }

        function Md2PosterHeader({ children }) {
            return (
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '20px',
                    fontSize: '14px',
                    opacity: 0.9
                }}>
                    {children}
                </div>
            );
        }

        function Md2PosterContent({ children }) {
            // 简单的 Markdown 渲染（实际项目中会更复杂）
            const renderMarkdown = (text) => {
                return text
                    .replace(/^# (.*$)/gm, '<h1 style="font-size: 24px; margin: 10px 0;">$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2 style="font-size: 20px; margin: 8px 0;">$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3 style="font-size: 18px; margin: 6px 0;">$1</h3>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/^> (.*$)/gm, '<blockquote style="border-left: 3px solid rgba(255,255,255,0.5); padding-left: 15px; margin: 10px 0; font-style: italic;">$1</blockquote>')
                    .replace(/^- (.*$)/gm, '<li style="margin: 5px 0;">$1</li>')
                    .replace(/^(\d+)\. (.*$)/gm, '<li style="margin: 5px 0;">$2</li>')
                    .replace(/`([^`]+)`/g, '<code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px; font-family: monospace;">$1</code>')
                    .replace(/^---$/gm, '<hr style="border: none; border-top: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">')
                    .replace(/\n/g, '<br>');
            };

            return (
                <div style={{ 
                    flex: 1,
                    fontSize: '16px',
                    lineHeight: '1.6'
                }} dangerouslySetInnerHTML={{ __html: renderMarkdown(children) }}>
                </div>
            );
        }

        function Md2PosterFooter({ children }) {
            return (
                <div style={{ 
                    textAlign: 'center',
                    marginTop: '20px',
                    fontSize: '12px',
                    opacity: 0.8
                }}>
                    {children}
                </div>
            );
        }

        function App() {
            const [selectedTheme, setSelectedTheme] = useState('SpringGradientWave');
            const [selectedSize, setSelectedSize] = useState('mobile');

            const themes = [
                { value: 'SpringGradientWave', label: '🌸 春季渐变波浪' },
                { value: 'SummerSunset', label: '🌅 夏日日落' },
                { value: 'AutumnLeaves', label: '🍂 秋叶' },
                { value: 'WinterSnow', label: '❄️ 冬雪' },
                { value: 'OceanBreeze', label: '🌊 海洋微风' },
                { value: 'ForestGreen', label: '🌲 森林绿' },
                { value: 'DesertSand', label: '🏜️ 沙漠沙' },
                { value: 'NightSky', label: '🌙 夜空' },
                { value: 'RoseGold', label: '🌹 玫瑰金' }
            ];

            const sizes = [
                { value: 'mobile', label: '📱 移动端' },
                { value: 'desktop', label: '💻 桌面端' },
                { value: 'square', label: '⬜ 正方形' }
            ];

            const markdown = `# 🚀 我的第一个海报

> 使用 markdown-to-image 创建漂亮的社交媒体图片

## ✨ 主要特点

- **简单易用** - 只需要写 Markdown
- **多种主题** - 9 种精美主题可选
- **响应式设计** - 适配不同尺寸
- **一键导出** - 轻松保存为图片

### 📊 使用场景

1. 社交媒体分享
2. 技术文档展示
3. 学习笔记整理
4. 产品功能介绍

### 💻 代码示例

\`console.log("Hello, markdown-to-image!");\`

---

**🎯 开始你的创作之旅吧！**`;

            return (
                <div>
                    <div className="demo-section">
                        <h2>🛠️ 控制面板</h2>
                        <div style={{ marginBottom: '20px' }}>
                            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                                🎨 选择主题：
                            </label>
                            <select 
                                value={selectedTheme} 
                                onChange={(e) => setSelectedTheme(e.target.value)}
                                style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ddd', width: '200px' }}
                            >
                                {themes.map(t => (
                                    <option key={t.value} value={t.value}>{t.label}</option>
                                ))}
                            </select>
                        </div>
                        
                        <div style={{ marginBottom: '20px' }}>
                            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                                📐 选择尺寸：
                            </label>
                            <select 
                                value={selectedSize} 
                                onChange={(e) => setSelectedSize(e.target.value)}
                                style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ddd', width: '200px' }}
                            >
                                {sizes.map(s => (
                                    <option key={s.value} value={s.value}>{s.label}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div className="demo-section">
                        <h2>👀 实时预览</h2>
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                            <Md2Poster theme={selectedTheme} size={selectedSize}>
                                <Md2PosterHeader>
                                    <span>@我的用户名</span>
                                    <span>{new Date().toISOString().slice(0, 10)}</span>
                                </Md2PosterHeader>
                                <Md2PosterContent>{markdown}</Md2PosterContent>
                                <Md2PosterFooter>
                                    <span>由 markdown-to-image 强力驱动</span>
                                </Md2PosterFooter>
                            </Md2Poster>
                        </div>
                        
                        <div style={{ 
                            marginTop: '20px', 
                            padding: '15px', 
                            backgroundColor: '#e3f2fd', 
                            borderRadius: '6px'
                        }}>
                            <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>💡 使用说明：</h4>
                            <ol style={{ margin: 0, paddingLeft: '20px' }}>
                                <li>在你的项目中运行：<code>npm install markdown-to-image react react-dom</code></li>
                                <li>导入组件：<code>import {`{Md2Poster}`} from 'markdown-to-image'</code></li>
                                <li>导入样式：<code>import 'markdown-to-image/dist/style.css'</code></li>
                                <li>使用组件创建你的海报</li>
                                <li>右键点击海报可以保存图片</li>
                            </ol>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('app'));
    </script>
</body>
</html>
