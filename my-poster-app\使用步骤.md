# 🎯 markdown-to-image 使用步骤

## 第一步：安装依赖

```bash
# 在你的项目目录中运行
npm install markdown-to-image react react-dom
```

## 第二步：创建组件

创建一个新的 React 组件文件，比如 `MyPoster.jsx`：

```jsx
import React from 'react';
import 'markdown-to-image/dist/style.css';
import { Md2Poster, Md2PosterContent, Md2PosterHeader, Md2PosterFooter } from 'markdown-to-image';

function MyPoster() {
  // 你的 Markdown 内容
  const markdown = `# 我的标题

> 这是一个引用

## 子标题

- 列表项 1
- 列表项 2
- 列表项 3

**粗体文字** 和 *斜体文字*

\`\`\`javascript
console.log("代码块");
\`\`\`

---

底部文字`;

  return (
    <Md2Poster theme="SpringGradientWave" size="mobile">
      <Md2PosterHeader className="flex justify-between items-center px-4">
        <span>@我的用户名</span>
        <span>{new Date().toISOString().slice(0, 10)}</span>
      </Md2PosterHeader>
      <Md2PosterContent>{markdown}</Md2PosterContent>
      <Md2PosterFooter className="flex justify-center items-center">
        <span>由 markdown-to-image 强力驱动</span>
      </Md2PosterFooter>
    </Md2Poster>
  );
}

export default MyPoster;
```

## 第三步：在应用中使用

在你的主应用文件中导入并使用：

```jsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import MyPoster from './MyPoster';

function App() {
  return (
    <div>
      <h1>我的海报应用</h1>
      <MyPoster />
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
```

## 第四步：自定义配置

### 更换主题
```jsx
<Md2Poster theme="SummerSunset" size="mobile">
  {/* 内容 */}
</Md2Poster>
```

可用主题：
- `SpringGradientWave` - 春季渐变波浪
- `SummerSunset` - 夏日日落
- `AutumnLeaves` - 秋叶
- `WinterSnow` - 冬雪
- `OceanBreeze` - 海洋微风
- `ForestGreen` - 森林绿
- `DesertSand` - 沙漠沙
- `NightSky` - 夜空
- `RoseGold` - 玫瑰金

### 更换尺寸
```jsx
<Md2Poster theme="SpringGradientWave" size="desktop">
  {/* 内容 */}
</Md2Poster>
```

可用尺寸：
- `mobile` - 移动端尺寸
- `desktop` - 桌面端尺寸
- `square` - 正方形尺寸

## 第五步：导出图片

用户可以：
1. 右键点击生成的海报
2. 选择"另存为图像"或"复制图像"
3. 保存到本地或复制到剪贴板

## 完整示例

这是一个完整的可运行示例：

```jsx
import React, { useState } from 'react';
import 'markdown-to-image/dist/style.css';
import { Md2Poster, Md2PosterContent, Md2PosterHeader, Md2PosterFooter } from 'markdown-to-image';

function PosterGenerator() {
  const [markdown, setMarkdown] = useState(`# 🚀 我的海报

> 使用 markdown-to-image 创建

## 特点

- **简单** - 只需写 Markdown
- **美观** - 多种主题可选
- **灵活** - 支持各种尺寸

### 代码示例

\`\`\`javascript
console.log("Hello World!");
\`\`\`

---

**开始创作吧！** ✨`);

  const [theme, setTheme] = useState('SpringGradientWave');
  const [size, setSize] = useState('mobile');

  return (
    <div style={{ padding: '20px' }}>
      <h1>海报生成器</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <label>主题：</label>
        <select value={theme} onChange={(e) => setTheme(e.target.value)}>
          <option value="SpringGradientWave">春季渐变波浪</option>
          <option value="SummerSunset">夏日日落</option>
          <option value="AutumnLeaves">秋叶</option>
          <option value="WinterSnow">冬雪</option>
        </select>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label>尺寸：</label>
        <select value={size} onChange={(e) => setSize(e.target.value)}>
          <option value="mobile">移动端</option>
          <option value="desktop">桌面端</option>
          <option value="square">正方形</option>
        </select>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label>Markdown 内容：</label>
        <textarea
          value={markdown}
          onChange={(e) => setMarkdown(e.target.value)}
          rows="10"
          cols="50"
          style={{ width: '100%', padding: '10px' }}
        />
      </div>

      <Md2Poster theme={theme} size={size}>
        <Md2PosterHeader className="flex justify-between items-center px-4">
          <span>@我的用户名</span>
          <span>{new Date().toISOString().slice(0, 10)}</span>
        </Md2PosterHeader>
        <Md2PosterContent>{markdown}</Md2PosterContent>
        <Md2PosterFooter className="flex justify-center items-center">
          <span>由 markdown-to-image 强力驱动</span>
        </Md2PosterFooter>
      </Md2Poster>
    </div>
  );
}

export default PosterGenerator;
```

## 🎯 现在你可以：

1. ✅ 安装 markdown-to-image 包
2. ✅ 创建你的第一个海报组件
3. ✅ 自定义主题和尺寸
4. ✅ 编辑 Markdown 内容
5. ✅ 导出漂亮的图片

开始创建你的第一个海报吧！🚀
