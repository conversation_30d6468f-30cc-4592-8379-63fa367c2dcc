<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 转图片工具</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .app-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 80vh;
        }
        
        .control-panel {
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }
        
        .preview-panel {
            padding: 30px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .poster-container {
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: transform 0.3s;
        }
        
        .poster-container:hover {
            transform: scale(1.02);
        }
        
        .tips {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-top: 20px;
            border-radius: 0 8px 8px 0;
        }
        
        .tips h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .template-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-panel {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🎨 Markdown 转图片工具</h1>
            <p>将你的 Markdown 文本转换为精美的社交媒体图片</p>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <div id="controls"></div>
            </div>
            
            <div class="preview-panel">
                <div id="preview"></div>
            </div>
        </div>
    </div>

    <script type="text/babel">
        const { useState, useRef } = React;

        // 海报组件
        function Poster({ theme, size, headerText, footerText, markdown }) {
            const themeStyles = {
                SpringGradientWave: {
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white'
                },
                SummerSunset: {
                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    color: 'white'
                },
                AutumnLeaves: {
                    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                    color: '#333'
                },
                WinterSnow: {
                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    color: '#333'
                },
                OceanBreeze: {
                    background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
                    color: 'white'
                },
                ForestGreen: {
                    background: 'linear-gradient(135deg, #00b894 0%, #00a085 100%)',
                    color: 'white'
                },
                DesertSand: {
                    background: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)',
                    color: 'white'
                },
                NightSky: {
                    background: 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)',
                    color: 'white'
                },
                RoseGold: {
                    background: 'linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%)',
                    color: 'white'
                }
            };

            const sizeStyles = {
                mobile: { width: '375px', minHeight: '600px' },
                desktop: { width: '800px', minHeight: '600px' },
                square: { width: '500px', minHeight: '500px' }
            };

            const renderMarkdown = (text) => {
                return text
                    .replace(/^# (.*$)/gm, '<h1 style="font-size: 28px; margin: 15px 0; font-weight: bold;">$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2 style="font-size: 24px; margin: 12px 0; font-weight: bold;">$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3 style="font-size: 20px; margin: 10px 0; font-weight: bold;">$1</h3>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong style="font-weight: bold;">$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em style="font-style: italic;">$1</em>')
                    .replace(/^> (.*$)/gm, '<blockquote style="border-left: 4px solid rgba(255,255,255,0.5); padding-left: 15px; margin: 15px 0; font-style: italic; opacity: 0.9;">$1</blockquote>')
                    .replace(/^- (.*$)/gm, '<div style="margin: 8px 0;">• $1</div>')
                    .replace(/^(\d+)\. (.*$)/gm, '<div style="margin: 8px 0;">$1. $2</div>')
                    .replace(/`([^`]+)`/g, '<code style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 0.9em;">$1</code>')
                    .replace(/^---$/gm, '<hr style="border: none; border-top: 2px solid rgba(255,255,255,0.3); margin: 25px 0;">')
                    .replace(/\n\n/g, '<br><br>')
                    .replace(/\n/g, '<br>');
            };

            return (
                <div style={{
                    ...sizeStyles[size],
                    ...themeStyles[theme],
                    borderRadius: '15px',
                    padding: '30px',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative'
                }}>
                    {/* 头部 */}
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '25px',
                        fontSize: '14px',
                        opacity: 0.8,
                        fontWeight: '500'
                    }}>
                        <span>{headerText}</span>
                        <span>{new Date().toLocaleDateString('zh-CN')}</span>
                    </div>

                    {/* 内容 */}
                    <div style={{ 
                        flex: 1,
                        fontSize: '16px',
                        lineHeight: '1.6'
                    }} dangerouslySetInnerHTML={{ __html: renderMarkdown(markdown) }}>
                    </div>

                    {/* 底部 */}
                    <div style={{ 
                        textAlign: 'center',
                        marginTop: '25px',
                        fontSize: '12px',
                        opacity: 0.7,
                        fontWeight: '500'
                    }}>
                        {footerText}
                    </div>
                </div>
            );
        }

        // 主应用
        function App() {
            const [markdown, setMarkdown] = useState(`# 🚀 我的第一个海报

> 使用这个工具创建漂亮的社交媒体图片

## ✨ 主要特点

- **简单易用** - 只需要写 Markdown
- **多种主题** - 9 种精美主题可选
- **响应式设计** - 适配不同尺寸
- **一键导出** - 轻松保存为图片

### 📊 使用场景

1. 社交媒体分享
2. 技术文档展示
3. 学习笔记整理
4. 产品功能介绍

### 💻 代码示例

\`console.log("Hello, World!");\`

---

**🎯 开始你的创作之旅吧！**`);

            const [theme, setTheme] = useState('SpringGradientWave');
            const [size, setSize] = useState('mobile');
            const [headerText, setHeaderText] = useState('@我的用户名');
            const [footerText, setFooterText] = useState('由 Markdown 转图片工具创建');
            
            const posterRef = useRef(null);

            const themes = [
                { value: 'SpringGradientWave', label: '🌸 春季渐变波浪' },
                { value: 'SummerSunset', label: '🌅 夏日日落' },
                { value: 'AutumnLeaves', label: '🍂 秋叶' },
                { value: 'WinterSnow', label: '❄️ 冬雪' },
                { value: 'OceanBreeze', label: '🌊 海洋微风' },
                { value: 'ForestGreen', label: '🌲 森林绿' },
                { value: 'DesertSand', label: '🏜️ 沙漠沙' },
                { value: 'NightSky', label: '🌙 夜空' },
                { value: 'RoseGold', label: '🌹 玫瑰金' }
            ];

            const sizes = [
                { value: 'mobile', label: '📱 移动端' },
                { value: 'desktop', label: '💻 桌面端' },
                { value: 'square', label: '⬜ 正方形' }
            ];

            const templates = {
                tech: `# 🔥 技术分享

> 今天学到的新技术

## React 开发技巧

### 核心概念
- 组件化开发
- 状态管理
- 生命周期

### 代码示例

\`const [state, setState] = useState(0);\`

### 最佳实践

1. 保持组件简单
2. 合理使用 Hooks
3. 优化性能

---

**持续学习，持续进步！** 💪`,

                daily: `# 📅 每日总结

> ${new Date().toLocaleDateString('zh-CN')} 的收获

## 今日完成

✅ 学习了新工具的使用  
✅ 完成了重要任务  
✅ 解决了技术难题  

## 今日感悟

**成长来自于每天的积累**

坚持做好每一件小事，时间会给你答案。

## 明日计划

🎯 继续深入学习  
🎯 完善项目功能  
🎯 分享学习心得  

---

*每天进步一点点 🌟*`,

                quote: `# 💭 今日金句

> "成功不是终点，失败不是末日，继续前进的勇气才最可贵。"
> 
> —— 温斯顿·丘吉尔

## 人生感悟

- **坚持** 是成功的关键
- **学习** 是进步的阶梯
- **分享** 是快乐的源泉

### 🎯 行动指南

1. 设定明确目标
2. 制定行动计划
3. 坚持不懈执行
4. 及时总结反思

---

**相信自己，你比想象中更强大！** ✨`
            };

            const applyTemplate = (templateKey) => {
                setMarkdown(templates[templateKey]);
            };

            const downloadImage = async () => {
                if (posterRef.current) {
                    try {
                        const canvas = await html2canvas(posterRef.current, {
                            backgroundColor: null,
                            scale: 2
                        });
                        
                        const link = document.createElement('a');
                        link.download = `海报_${new Date().getTime()}.png`;
                        link.href = canvas.toDataURL();
                        link.click();
                    } catch (error) {
                        alert('导出失败，请尝试右键点击海报选择"另存为图像"');
                    }
                }
            };

            return (
                <>
                    {/* 控制面板 */}
                    <div>
                        <h2 style={{ marginBottom: '20px', color: '#333' }}>🛠️ 控制面板</h2>
                        
                        {/* 快速模板 */}
                        <div className="form-group">
                            <label className="form-label">📋 快速模板：</label>
                            <div className="template-buttons">
                                <button className="btn btn-primary" onClick={() => applyTemplate('tech')}>
                                    💻 技术分享
                                </button>
                                <button className="btn btn-primary" onClick={() => applyTemplate('daily')}>
                                    📅 每日总结
                                </button>
                                <button className="btn btn-primary" onClick={() => applyTemplate('quote')}>
                                    💭 励志语录
                                </button>
                            </div>
                        </div>

                        {/* 主题选择 */}
                        <div className="form-group">
                            <label className="form-label">🎨 选择主题：</label>
                            <select 
                                className="form-control"
                                value={theme} 
                                onChange={(e) => setTheme(e.target.value)}
                            >
                                {themes.map(t => (
                                    <option key={t.value} value={t.value}>{t.label}</option>
                                ))}
                            </select>
                        </div>

                        {/* 尺寸选择 */}
                        <div className="form-group">
                            <label className="form-label">📐 选择尺寸：</label>
                            <select 
                                className="form-control"
                                value={size} 
                                onChange={(e) => setSize(e.target.value)}
                            >
                                {sizes.map(s => (
                                    <option key={s.value} value={s.value}>{s.label}</option>
                                ))}
                            </select>
                        </div>

                        {/* 头部文字 */}
                        <div className="form-group">
                            <label className="form-label">📝 头部文字：</label>
                            <input
                                className="form-control"
                                type="text"
                                value={headerText}
                                onChange={(e) => setHeaderText(e.target.value)}
                                placeholder="输入头部显示的文字"
                            />
                        </div>

                        {/* 底部文字 */}
                        <div className="form-group">
                            <label className="form-label">📝 底部文字：</label>
                            <input
                                className="form-control"
                                type="text"
                                value={footerText}
                                onChange={(e) => setFooterText(e.target.value)}
                                placeholder="输入底部显示的文字"
                            />
                        </div>

                        {/* Markdown 编辑器 */}
                        <div className="form-group">
                            <label className="form-label">✏️ 编辑 Markdown：</label>
                            <textarea
                                className="form-control"
                                value={markdown}
                                onChange={(e) => setMarkdown(e.target.value)}
                                rows="12"
                                style={{ fontFamily: 'Consolas, Monaco, monospace', fontSize: '13px' }}
                                placeholder="在这里输入你的 Markdown 内容..."
                            />
                        </div>

                        {/* 操作按钮 */}
                        <div>
                            <button className="btn btn-success" onClick={downloadImage}>
                                💾 下载图片
                            </button>
                            <button className="btn btn-danger" onClick={() => setMarkdown('')}>
                                🗑️ 清空内容
                            </button>
                        </div>

                        <div className="tips">
                            <h4>💡 使用提示：</h4>
                            <ul style={{ margin: 0, paddingLeft: '20px' }}>
                                <li>支持标准 Markdown 语法</li>
                                <li>可以使用快速模板开始</li>
                                <li>尝试不同主题和尺寸</li>
                                <li>点击"下载图片"保存海报</li>
                                <li>也可以右键海报选择"另存为图像"</li>
                            </ul>
                        </div>
                    </div>

                    {/* 预览区域 */}
                    <div>
                        <h2 style={{ marginBottom: '20px', color: '#333' }}>👀 实时预览</h2>
                        
                        <div className="poster-container" ref={posterRef}>
                            <Poster 
                                theme={theme}
                                size={size}
                                headerText={headerText}
                                footerText={footerText}
                                markdown={markdown}
                            />
                        </div>
                    </div>
                </>
            );
        }

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('controls'));
        ReactDOM.render(<div></div>, document.getElementById('preview'));
        
        // 将预览移到右侧
        setTimeout(() => {
            const previewContent = document.querySelector('#controls').lastElementChild;
            document.getElementById('preview').appendChild(previewContent);
        }, 100);
    </script>
</body>
</html>
