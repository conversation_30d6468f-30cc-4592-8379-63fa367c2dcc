import React, { useState, useRef } from 'react';
import 'markdown-to-image/dist/style.css';
import { Md2Poster, Md2PosterContent, Md2PosterHeader, Md2PosterFooter } from 'markdown-to-image';

// 实用的 Markdown 转图片应用
function MarkdownPosterApp() {
  // 状态管理
  const [markdown, setMarkdown] = useState(`# 🚀 我的第一个海报

> 使用 markdown-to-image 创建漂亮的社交媒体图片

## ✨ 主要特点

- **简单易用** - 只需要写 Markdown
- **多种主题** - 9 种精美主题可选
- **响应式设计** - 适配不同尺寸
- **一键导出** - 轻松保存为图片

### 📊 使用场景

1. 社交媒体分享
2. 技术文档展示  
3. 学习笔记整理
4. 产品功能介绍

### 💻 代码示例

\`\`\`javascript
import { Md2Poster } from 'markdown-to-image';

function MyPoster() {
  return (
    <Md2Poster theme="SpringGradientWave">
      <Md2PosterContent>{markdown}</Md2PosterContent>
    </Md2Poster>
  );
}
\`\`\`

---

**🎯 开始你的创作之旅吧！**`);

  const [theme, setTheme] = useState('SpringGradientWave');
  const [size, setSize] = useState('mobile');
  const [headerText, setHeaderText] = useState('@我的用户名');
  const [footerText, setFooterText] = useState('由 markdown-to-image 强力驱动');
  
  const posterRef = useRef(null);

  // 可用主题列表
  const themes = [
    { value: 'SpringGradientWave', label: '🌸 春季渐变波浪' },
    { value: 'SummerSunset', label: '🌅 夏日日落' },
    { value: 'AutumnLeaves', label: '🍂 秋叶' },
    { value: 'WinterSnow', label: '❄️ 冬雪' },
    { value: 'OceanBreeze', label: '🌊 海洋微风' },
    { value: 'ForestGreen', label: '🌲 森林绿' },
    { value: 'DesertSand', label: '🏜️ 沙漠沙' },
    { value: 'NightSky', label: '🌙 夜空' },
    { value: 'RoseGold', label: '🌹 玫瑰金' }
  ];

  // 尺寸选项
  const sizes = [
    { value: 'mobile', label: '📱 移动端 (适合手机分享)' },
    { value: 'desktop', label: '💻 桌面端 (适合电脑展示)' },
    { value: 'square', label: '⬜ 正方形 (适合社交媒体)' }
  ];

  // 预设模板
  const templates = {
    tech: `# 🔥 技术分享

> 今天学到的新技术

## React Hooks 使用技巧

### useState 基础用法
\`\`\`javascript
const [count, setCount] = useState(0);
\`\`\`

### useEffect 生命周期
- 组件挂载时执行
- 依赖项变化时执行
- 组件卸载时清理

**💡 记住：Hooks 让函数组件更强大！**`,

    daily: `# 📅 每日总结

> ${new Date().toLocaleDateString('zh-CN')} 的收获

## 今日完成

✅ 学习了 markdown-to-image  
✅ 创建了第一个海报  
✅ 掌握了基本用法  

## 明日计划

🎯 深入学习 React  
🎯 尝试更多主题样式  
🎯 制作更多创意海报  

---

*每天进步一点点 🌟*`,

    quote: `# 💭 今日金句

> "代码是写给人看的，只是顺便能在机器上运行。"
> 
> —— Harold Abelson

## 编程智慧

- **简洁胜过复杂**
- **可读性很重要**  
- **测试是必需的**
- **文档要跟上**

### 🎯 行动指南

1. 写清晰的代码
2. 添加有意义的注释
3. 保持代码整洁
4. 持续学习改进

---

**让代码成为艺术 ✨**`
  };

  // 应用模板
  const applyTemplate = (templateKey) => {
    setMarkdown(templates[templateKey]);
  };

  // 复制图片功能（需要用户手动操作）
  const copyImage = () => {
    alert('请右键点击海报图片，选择"复制图像"来复制到剪贴板');
  };

  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      maxWidth: '1200px', 
      margin: '0 auto', 
      padding: '20px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1 style={{ textAlign: 'center', color: '#333', marginBottom: '30px' }}>
        🎨 Markdown 转图片工具
      </h1>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        {/* 左侧控制面板 */}
        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <h2 style={{ marginTop: 0, color: '#333' }}>🛠️ 控制面板</h2>
          
          {/* 快速模板 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              📋 快速模板：
            </label>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button onClick={() => applyTemplate('tech')} style={buttonStyle}>
                💻 技术分享
              </button>
              <button onClick={() => applyTemplate('daily')} style={buttonStyle}>
                📅 每日总结
              </button>
              <button onClick={() => applyTemplate('quote')} style={buttonStyle}>
                💭 励志语录
              </button>
            </div>
          </div>

          {/* 主题选择 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              🎨 选择主题：
            </label>
            <select 
              value={theme} 
              onChange={(e) => setTheme(e.target.value)}
              style={selectStyle}
            >
              {themes.map(t => (
                <option key={t.value} value={t.value}>{t.label}</option>
              ))}
            </select>
          </div>

          {/* 尺寸选择 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              📐 选择尺寸：
            </label>
            <select 
              value={size} 
              onChange={(e) => setSize(e.target.value)}
              style={selectStyle}
            >
              {sizes.map(s => (
                <option key={s.value} value={s.value}>{s.label}</option>
              ))}
            </select>
          </div>

          {/* 头部文字 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              📝 头部文字：
            </label>
            <input
              type="text"
              value={headerText}
              onChange={(e) => setHeaderText(e.target.value)}
              style={inputStyle}
              placeholder="输入头部显示的文字"
            />
          </div>

          {/* 底部文字 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              📝 底部文字：
            </label>
            <input
              type="text"
              value={footerText}
              onChange={(e) => setFooterText(e.target.value)}
              style={inputStyle}
              placeholder="输入底部显示的文字"
            />
          </div>

          {/* Markdown 编辑器 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              ✏️ 编辑 Markdown：
            </label>
            <textarea
              value={markdown}
              onChange={(e) => setMarkdown(e.target.value)}
              style={{
                width: '100%',
                height: '300px',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontFamily: 'Consolas, Monaco, monospace',
                fontSize: '14px',
                resize: 'vertical'
              }}
              placeholder="在这里输入你的 Markdown 内容..."
            />
          </div>

          {/* 操作按钮 */}
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            <button onClick={copyImage} style={{...buttonStyle, backgroundColor: '#28a745'}}>
              📋 复制图片
            </button>
            <button 
              onClick={() => setMarkdown('')} 
              style={{...buttonStyle, backgroundColor: '#dc3545'}}
            >
              🗑️ 清空内容
            </button>
          </div>
        </div>

        {/* 右侧预览区域 */}
        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <h2 style={{ marginTop: 0, color: '#333' }}>👀 实时预览</h2>
          
          <div style={{ 
            border: '2px dashed #ddd', 
            borderRadius: '8px', 
            padding: '20px',
            backgroundColor: '#fafafa',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px'
          }}>
            <div ref={posterRef}>
              <Md2Poster theme={theme} size={size}>
                <Md2PosterHeader className="flex justify-between items-center px-4">
                  <span>{headerText}</span>
                  <span>{new Date().toISOString().slice(0, 10)}</span>
                </Md2PosterHeader>
                <Md2PosterContent>{markdown}</Md2PosterContent>
                <Md2PosterFooter className="flex justify-center items-center">
                  <span>{footerText}</span>
                </Md2PosterFooter>
              </Md2Poster>
            </div>
          </div>

          {/* 使用说明 */}
          <div style={{ 
            marginTop: '20px', 
            padding: '15px', 
            backgroundColor: '#e3f2fd', 
            borderRadius: '6px',
            fontSize: '14px'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>💡 使用提示：</h4>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li>支持标准 Markdown 语法</li>
              <li>可以插入代码块、列表、引用等</li>
              <li>右键点击海报可以保存图片</li>
              <li>尝试不同主题和尺寸组合</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// 样式定义
const buttonStyle = {
  padding: '8px 16px',
  border: 'none',
  borderRadius: '4px',
  backgroundColor: '#007bff',
  color: 'white',
  cursor: 'pointer',
  fontSize: '14px'
};

const selectStyle = {
  width: '100%',
  padding: '8px',
  border: '1px solid #ddd',
  borderRadius: '4px',
  fontSize: '14px'
};

const inputStyle = {
  width: '100%',
  padding: '8px',
  border: '1px solid #ddd',
  borderRadius: '4px',
  fontSize: '14px'
};

export default MarkdownPosterApp;
