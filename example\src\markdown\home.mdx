# Markdown To Image

This React component renders Markdown as visually appealing social media images. The project also includes a built-in web editor that can be used as an online Markdown-to-poster editor with a simple one-click deployment.

- [English](https://github.com/gcui-art/markdown-to-image) | [中文](https://github.com/gcui-art/markdown-to-image/blob/main/README_CN.md)
- [Deploy Editor with Vercel](https://vercel.com/new/clone?repository-url=https://github.com/gcui-art/markdown-to-image&root-directory=example&project-name=markdown-to-image&repository-name=markdown-to-image)
- [NPM:markdown-to-image](https://www.npmjs.com/package/markdown-to-image)

⭐ [Click on Star and Watch to stay updated with our latest developments.](https://github.com/gcui-art/markdown-to-image)

Made By [Readpo](https://github.com/readpo) With ❤️

## Features

- [x] Render Markdown as poster images optimized for social sharing
- [x] One built-in templates with support for custom templates
- [x] Customizable themes with 9 pre-built options
- [x] Copy output as an image
- [x] One-click deployment to platforms like Vercel
- [x] Integrated image CORS proxy for easy insertion of online images into posters
- [x] Copy output as HTML code for pasting into emails and editors
- [ ] More built-in templates

## Getting Started

There are two ways to use markdown-to-image:

1. Integration: markdown-to-image is exported as a React component that can be seamlessly integrated into your projects.
2. Using Web Editor: The example path includes a web editor that can be deployed and used as an online editor.

   [Read More >>](https://github.com/gcui-art/markdown-to-image)

⭐ [Click on Star and Watch to stay updated with our latest developments.](https://github.com/gcui-art/markdown-to-image)
