@echo off
echo.
echo ========================================
echo    Markdown 转图片工具 启动中...
echo ========================================
echo.
echo 正在打开浏览器...
echo.

REM 获取当前目录的完整路径
set "current_dir=%~dp0"
set "html_file=%current_dir%index.html"

REM 打开默认浏览器
start "" "%html_file%"

echo 工具已在浏览器中打开！
echo.
echo 使用说明：
echo 1. 在左侧编辑 Markdown 内容
echo 2. 选择喜欢的主题和尺寸
echo 3. 点击"下载图片"保存海报
echo 4. 也可以右键海报选择"另存为图像"
echo.
echo 按任意键关闭此窗口...
pause >nul
