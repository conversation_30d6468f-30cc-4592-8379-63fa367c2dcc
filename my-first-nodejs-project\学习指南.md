# Node.js 和 npm 入门指南

## 🎯 什么是 Node.js 和 npm？

- **Node.js**：让 JavaScript 可以在服务器端运行的平台
- **npm**：Node.js 的包管理器，用来安装和管理第三方库

## 📁 项目结构

```
my-first-nodejs-project/
├── package.json    # 项目配置文件
├── app.js         # 主程序文件
├── hello.txt      # 程序生成的文件
└── 学习指南.md    # 这个文件
```

## 🚀 基本命令

### Node.js 命令
```bash
# 运行 JavaScript 文件
node app.js

# 查看 Node.js 版本
node --version

# 进入交互模式（可以直接输入 JavaScript 代码）
node
```

### npm 命令
```bash
# 查看 npm 版本
npm --version

# 初始化新项目（创建 package.json）
npm init

# 快速初始化（使用默认设置）
npm init -y

# 安装包到项目
npm install 包名

# 全局安装包
npm install -g 包名

# 安装开发依赖
npm install --save-dev 包名

# 卸载包
npm uninstall 包名

# 查看已安装的包
npm list

# 运行 package.json 中定义的脚本
npm run 脚本名
npm start  # 运行 start 脚本
```

## 📝 package.json 文件说明

```json
{
  "name": "项目名称",
  "version": "版本号",
  "description": "项目描述",
  "main": "入口文件",
  "scripts": {
    "start": "node app.js",    // npm start 时执行
    "dev": "node app.js",      // npm run dev 时执行
    "test": "测试命令"
  },
  "dependencies": {
    // 生产环境依赖的包
  },
  "devDependencies": {
    // 开发环境依赖的包
  }
}
```

## 🔧 常用内置模块

### 1. fs (文件系统)
```javascript
const fs = require('fs');

// 读取文件
const content = fs.readFileSync('文件名', 'utf8');

// 写入文件
fs.writeFileSync('文件名', '内容');
```

### 2. path (路径处理)
```javascript
const path = require('path');

// 连接路径
const fullPath = path.join(__dirname, 'folder', 'file.txt');

// 获取文件扩展名
const ext = path.extname('file.txt'); // .txt
```

### 3. os (操作系统)
```javascript
const os = require('os');

console.log(os.type());      // 操作系统类型
console.log(os.totalmem());  // 总内存
console.log(os.userInfo());  // 用户信息
```

## 📦 安装和使用第三方包

### 示例：安装 lodash 工具库
```bash
npm install lodash
```

### 在代码中使用
```javascript
const _ = require('lodash');

const numbers = [1, 2, 3, 4, 5];
const sum = _.sum(numbers);
console.log(sum); // 15
```

## 🌐 创建简单的 Web 服务器

```javascript
const http = require('http');

const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end('<h1>你好，世界！</h1>');
});

server.listen(3000, () => {
    console.log('服务器运行在 http://localhost:3000');
});
```

## 💡 学习建议

### 1. 基础阶段
- 熟悉 JavaScript 语法
- 学会使用 Node.js 内置模块
- 理解 package.json 的作用
- 练习文件操作和基本编程

### 2. 进阶阶段
- 学习使用第三方包
- 了解异步编程（Promise、async/await）
- 学习创建 Web 服务器
- 了解数据库操作

### 3. 实战阶段
- 创建完整的 Web 应用
- 学习框架（如 Express.js）
- 部署应用到服务器
- 学习测试和调试

## 🔗 有用的资源

- [Node.js 官方文档](https://nodejs.org/zh-cn/docs/)
- [npm 官方网站](https://www.npmjs.com/)
- [JavaScript 教程](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript)

## 🎯 下一步

1. 修改 `app.js` 文件，尝试添加新功能
2. 安装一个第三方包并使用它
3. 创建一个简单的 Web 服务器
4. 学习使用 Express.js 框架

记住：编程最好的学习方法就是多练习！🚀
