{"name": "test-project", "version": "1.0.0", "description": "Markdown to Image 使用示例", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "example": "node example.js", "serve": "npx http-server . -p 3000 -o"}, "keywords": ["markdown", "image", "poster", "react"], "author": "", "license": "ISC", "dependencies": {"markdown-to-image": "^0.0.13", "react": "^19.1.1", "react-dom": "^19.1.1"}}