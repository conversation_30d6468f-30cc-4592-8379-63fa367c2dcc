{"name": "markdown-image-tutorial", "version": "1.0.0", "description": "学习使用 markdown-to-image 的实战项目", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "build": "vite build", "preview": "vite preview"}, "keywords": ["markdown", "image", "tutorial"], "author": "学习者", "license": "MIT", "dependencies": {"express": "^4.18.2", "markdown-to-image": "^0.0.13", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "vite": "^5.2.0"}}