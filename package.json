{"name": "markdown-to-image", "version": "0.0.12", "type": "module", "license": "Apache-2.0", "description": "markdown-to-image component renders Markdown as visually appealing social media images. The project also includes a built-in web editor that can be used as an online Markdown-to-poster editor with a simple one-click deployment.", "keywords": ["markdown", "image", "poster", "react", "vite", "tailwindcss", "typescript"], "homepage": "https://readpo.com/poster", "repository": {"type": "git", "url": "git+https://github.com/gcui-art/markdown-to-image.git"}, "files": ["dist", "package.json", "README.md", "LICENSE"], "main": "./dist/markdown-to-image.js", "module": "./dist/markdown-to-image.js", "types": "./dist/packages/index.d.ts", "exports": {"./dist/style.css": "./dist/style.css", ".": {"import": "./dist/markdown-to-image.js", "types": "./dist/packages/index.d.ts"}}, "scripts": {"dev": "vite", "build": "tsc --p ./tsconfig-build.json && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "example": "cd example && npm install && npm run dev"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@chromatic-com/storybook": "^1.3.3", "@rollup/plugin-typescript": "^11.1.6", "@storybook/addon-essentials": "^8.0.9", "@storybook/addon-interactions": "^8.0.9", "@storybook/addon-links": "^8.0.9", "@storybook/addon-onboarding": "^8.0.9", "@storybook/blocks": "^8.0.9", "@storybook/react": "^8.0.9", "@storybook/react-vite": "^8.0.9", "@storybook/test": "^8.0.9", "@tailwindcss/typography": "^0.5.12", "@types/node": "^20.12.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "eslint-plugin-storybook": "^0.8.0", "path": "^0.12.7", "postcss": "^8.4.38", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^8.0.9", "tailwindcss": "^3.4.3", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-dts": "^3.9.0"}, "dependencies": {"@uiw/react-md-editor": "^4.0.5", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "modern-screenshot": "^4.4.39", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.3.0"}}